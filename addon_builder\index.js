"use strict";
const fs = require("fs");
const path = require("path");
const JavaScriptObfuscator = require("javascript-obfuscator");
const globCallback = require("glob");
const defSettings = {
    obfuscate: true,
    singleLine: false,
    comments: true,
    packs: ["RP", "BP"],
    jsObfuscation: {
        enabled: true,
        options: {
            compact: true,
            controlFlowFlattening: true,
            controlFlowFlatteningThreshold: 0.4,
            deadCodeInjection: true,
            deadCodeInjectionThreshold: 0.4,
            debugProtection: false,
            debugProtectionInterval: 0,
            disableConsoleOutput: false,
            identifierNamesGenerator: "hexadecimal",
            log: false,
            renameGlobals: true,
            rotateStringArray: true,
            selfDefending: true,
            shuffleStringArray: true,
            splitStrings: true,
            splitStringsChunkLength: 10,
            stringArray: true,
            stringArrayEncoding: [],
            stringArrayThreshold: 0.75,
            transformObjectKeys: true,
            unicodeEscapeSequence: true,
            target: "node"
        }
    }
};
const settings = Object.assign(defSettings, process.argv[2] ? JSON.parse(process.argv[2]) : {});
const unicodeEscape = (str = "") => {
    let result = "";
    let charCode = str.charCodeAt(0);
    for (let i = 0; !isNaN(charCode); i++) {
        result += `\\u${`0000${charCode.toString(16)}`.slice(-4)}`;
        charCode = str.charCodeAt(i + 1);
    }
    return result;
};
const randomCharStr = (length) => {
    let result = "";
    const getChar = () => {
        let result = String.fromCharCode(Math.random() * 200 + 100);
        return result ? result : getChar();
    };
    for (let i = 0; i < length; i++)
        result += getChar();
    return result;
};
const commentLine = (str) => {
    const rand = () => (Math.random() > Math.random() ? `/*${randomCharStr(Math.random() * 40 + 5)}*/${Math.random() > 0.85 ? "\n" : ""}` : "");
    return `${rand()}${str}${rand()}`;
};
function obfuscateJavaScript(filePath) {
    try {
        const fileContent = fs.readFileSync(filePath, "utf8");
        if (!settings.jsObfuscation.enabled)
            return;
        const obfuscationResult = JavaScriptObfuscator.obfuscate(fileContent, settings.jsObfuscation.options);
        fs.writeFileSync(filePath, obfuscationResult.getObfuscatedCode());
        console.log(`Obfuscated JavaScript file: ${filePath}`);
    }
    catch (error) {
        console.error(`Error obfuscating JavaScript file ${filePath}:`, error instanceof Error ? error.message : error);
    }
}
function run(files) {
    files.forEach((filePath) => {
        if (filePath.toLowerCase().endsWith(".js") && settings.jsObfuscation.enabled) {
            obfuscateJavaScript(filePath);
            return;
        }
        let file = fs.readFileSync(filePath).toString();
        let contents = file.split("\n");
        let newContents = [];
        for (let i = 0; i < contents.length; i++) {
            const currentLine = contents[i];
            if (!currentLine)
                continue;
            let line = currentLine.trimEnd();
            if (settings.obfuscate) {
                let matches = line.match(/".+?"/g);
                if (matches) {
                    let key = matches[0].slice(1, -1);
                    line = line.replace(key, unicodeEscape(key));
                }
            }
            if (settings.comments)
                line = commentLine(line);
            newContents.push(line.replace(/\n/g, ""));
        }
        let res = settings.singleLine ? newContents.map((v) => v.trim()).join("") : newContents.join("\n");
        fs.writeFileSync(filePath, res);
    });
}
const glob = (pattern) => {
    return new Promise((resolve, reject) => {
        globCallback(pattern, (err, matches) => {
            if (err)
                reject(err);
            else
                resolve(matches);
        });
    });
};
async function processFiles() {
    for (let i = 0; i < settings.packs.length; i++) {
        try {
            const files = await glob(`${settings.packs[i]}/**/*.{json,js}`);
            const excludedFolders = [
                "/animations/",
                "/animation_controllers/",
                "/render_controllers/",
                "/particles/",
                "/spawn_rules/",
                "/blocks/",
                "/features/",
                "/feature_rules/",
                "/worldgen/",
                "/structures/",
                "/node_modules/"
            ];
            const filteredFiles = files.filter((file) => {
                return !file.includes("node_modules") && !file.endsWith("index.js") && !excludedFolders.some((folder) => file.includes(folder));
            });
            run(filteredFiles);
        }
        catch (error) {
            console.error("Error processing files:", error instanceof Error ? error.message : error);
        }
    }
}
function getAllFiles(dirPath, arrayOfFiles) {
    const files = fs.readdirSync(dirPath);
    arrayOfFiles = arrayOfFiles || [];
    files.forEach((file) => {
        const filePath = path.join(dirPath, file);
        if (fs.lstatSync(filePath).isDirectory()) {
            arrayOfFiles = getAllFiles(filePath, arrayOfFiles);
        }
        else {
            arrayOfFiles.push(filePath);
        }
    });
    return arrayOfFiles;
}
function textureList() {
    if (settings.packs.length === 0) {
        console.error("No packs specified in settings");
        return;
    }
    const firstPack = settings.packs[0];
    if (!firstPack) {
        console.error("First pack is undefined");
        return;
    }
    const texturesFolder = path.join(process.cwd(), firstPack, "textures");
    const textureListPath = path.join(texturesFolder, "texture_list.json");
    const textureExtensions = [".png", ".tga"];
    let textureFiles = [];
    try {
        const allFiles = getAllFiles(texturesFolder);
        textureFiles = allFiles
            .filter((file) => textureExtensions.some((ext) => file.endsWith(ext)))
            .map((file) => {
            let relativePath = path.relative(process.cwd(), file).replace(/\\/g, "/");
            relativePath = relativePath.replace(/^RP\//, "");
            relativePath = relativePath.replace(/\.(png|tga)$/, "");
            return relativePath;
        });
        textureFiles.sort((a, b) => a.length - b.length);
        fs.writeFileSync(textureListPath, JSON.stringify(textureFiles, null, 2));
        console.log(`Generated texture_list.json with ${textureFiles.length} entries.`);
    }
    catch (error) {
        console.error("Error processing texture files:", error instanceof Error ? error.message : error);
    }
}
function generateContents() {
    settings.packs.forEach((pack) => {
        const rootDir = path.join(process.cwd(), pack);
        const contentsPath = path.join(rootDir, "contents.json");
        try {
            const allFiles = getAllFiles(rootDir);
            const contentList = allFiles.map((file) => ({ path: path.relative(rootDir, file).replace(/\\/g, "/") }));
            contentList.sort((a, b) => a.path.localeCompare(b.path));
            const contentStructure = { content: contentList };
            fs.writeFileSync(contentsPath, JSON.stringify(contentStructure, null, 2));
            console.log(`Generated contents.json for ${pack} with ${contentList.length} entries.`);
        }
        catch (error) {
            console.error(`Error generating contents.json for ${pack}:`, error instanceof Error ? error.message : error);
        }
    });
}
(async () => {
    await processFiles();
    textureList();
    generateContents();
})().catch((error) => {
    console.error("Error in main process:", error instanceof Error ? error.message : error);
});
