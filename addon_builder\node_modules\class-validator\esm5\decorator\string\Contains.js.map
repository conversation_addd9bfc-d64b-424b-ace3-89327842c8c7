{"version": 3, "file": "Contains.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/Contains.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAU,CAAC;AAEnC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc,EAAE,IAAY;IACnD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,IAAY,EAAE,iBAAqC;IAC1E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAArC,CAAqC;YACzE,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,8CAA8C,EAA3D,CAA2D,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport containsValidator from 'validator/lib/contains';\n\nexport const CONTAINS = 'contains';\n\n/**\n * Checks if the string contains the seed.\n * If given value is not a string, then it returns false.\n */\nexport function contains(value: unknown, seed: string): boolean {\n  return typeof value === 'string' && containsValidator(value, seed);\n}\n\n/**\n * Checks if the string contains the seed.\n * If given value is not a string, then it returns false.\n */\nexport function Contains(seed: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: CONTAINS,\n      constraints: [seed],\n      validator: {\n        validate: (value, args): boolean => contains(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain a $constraint1 string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}