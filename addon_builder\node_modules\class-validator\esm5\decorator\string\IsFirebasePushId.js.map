{"version": 3, "file": "IsFirebasePushId.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsFirebasePushId.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,mBAAmB,GAAG,kBAAkB,CAAC;AAEtD;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAc;IAC7C,IAAM,YAAY,GAAG,kBAAkB,CAAC;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,iBAAqC;IACpE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,mBAAmB;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,gBAAgB,CAAC,KAAK,CAAC,EAAvB,CAAuB;YAC3D,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,sCAAsC,EAAnD,CAAmD,EACjE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_FIREBASE_PUSH_ID = 'IsFirebasePushId';\n\n/**\n * Checks if the string is a Firebase Push Id\n * If given value is not a Firebase Push Id, it returns false\n */\nexport function isFirebasePushId(value: unknown): boolean {\n  const webSafeRegex = /^[a-zA-Z0-9_-]*$/;\n  return typeof value === 'string' && value.length === 20 && webSafeRegex.test(value);\n}\n\n/**\n * Checks if the string is a Firebase Push Id\n * If given value is not a Firebase Push Id, it returns false\n */\nexport function IsFirebasePushId(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_FIREBASE_PUSH_ID,\n      validator: {\n        validate: (value, args): boolean => isFirebasePushId(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a Firebase Push Id',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}