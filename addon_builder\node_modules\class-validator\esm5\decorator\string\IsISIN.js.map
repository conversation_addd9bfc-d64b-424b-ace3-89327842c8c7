{"version": 3, "file": "IsISIN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISIN.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAEnD,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,MAAM,CAAC,KAAK,CAAC,EAAb,CAAa;YACjD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,uDAAuD,EAApE,CAAoE,EAClF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIsinValidator from 'validator/lib/isISIN';\n\nexport const IS_ISIN = 'isIsin';\n\n/**\n * Checks if the string is an ISIN (stock/security identifier).\n * If given value is not a string, then it returns false.\n */\nexport function isISIN(value: unknown): boolean {\n  return typeof value === 'string' && isIsinValidator(value);\n}\n\n/**\n * Checks if the string is an ISIN (stock/security identifier).\n * If given value is not a string, then it returns false.\n */\nexport function IsISIN(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISIN,\n      validator: {\n        validate: (value, args): boolean => isISIN(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an ISIN (stock/security identifier)',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}