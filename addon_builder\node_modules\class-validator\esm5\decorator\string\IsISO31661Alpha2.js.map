{"version": 3, "file": "IsISO31661Alpha2.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISO31661Alpha2.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,yBAAyB,MAAM,gCAAgC,CAAC;AAEvE,MAAM,CAAC,IAAM,mBAAmB,GAAG,kBAAkB,CAAC;AAEtD;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAc;IAC7C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,yBAAyB,CAAC,KAAK,CAAC,CAAC;AACvE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,iBAAqC;IACpE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,mBAAmB;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,gBAAgB,CAAC,KAAK,CAAC,EAAvB,CAAuB;YAC3D,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,gDAAgD,EAA7D,CAA6D,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISO31661Alpha2Validator from 'validator/lib/isISO31661Alpha2';\n\nexport const IS_ISO31661_ALPHA_2 = 'isISO31661Alpha2';\n\n/**\n * Check if the string is a valid [ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) officially assigned country code.\n */\nexport function isISO31661Alpha2(value: unknown): boolean {\n  return typeof value === 'string' && isISO31661Alpha2Validator(value);\n}\n\n/**\n * Check if the string is a valid [ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) officially assigned country code.\n */\nexport function IsISO31661Alpha2(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISO31661_ALPHA_2,\n      validator: {\n        validate: (value, args): boolean => isISO31661Alpha2(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO31661 Alpha2 code',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}