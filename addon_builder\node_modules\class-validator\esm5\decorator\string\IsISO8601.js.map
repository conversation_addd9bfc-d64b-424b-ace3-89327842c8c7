{"version": 3, "file": "IsISO8601.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISO8601.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,kBAAkB,MAAM,yBAAyB,CAAC;AAGzD,MAAM,CAAC,IAAM,UAAU,GAAG,WAAW,CAAC;AAEtC;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc,EAAE,OAAsC;IAC9E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzE,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,SAAS,CACvB,OAAsC,EACtC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,UAAU;QAChB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAtC,CAAsC;YAC1E,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,gDAAgD,EAA7D,CAA6D,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIso8601Validator from 'validator/lib/isISO8601';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ISO8601 = 'isIso8601';\n\n/**\n * Checks if the string is a valid ISO 8601 date.\n * If given value is not a string, then it returns false.\n * Use the option strict = true for additional checks for a valid date, e.g. invalidates dates like 2019-02-29.\n */\nexport function isISO8601(value: unknown, options?: ValidatorJS.IsISO8601Options): boolean {\n  return typeof value === 'string' && isIso8601Validator(value, options);\n}\n\n/**\n * Checks if the string is a valid ISO 8601 date.\n * If given value is not a string, then it returns false.\n * Use the option strict = true for additional checks for a valid date, e.g. invalidates dates like 2019-02-29.\n */\nexport function IsISO8601(\n  options?: ValidatorJS.IsISO8601Options,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISO8601,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isISO8601(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO 8601 date string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}