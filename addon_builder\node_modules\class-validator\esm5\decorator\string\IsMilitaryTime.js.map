{"version": 3, "file": "IsMilitaryTime.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMilitaryTime.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAErD,MAAM,CAAC,IAAM,gBAAgB,GAAG,gBAAgB,CAAC;AAEjD;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,KAAc;IAC3C,IAAM,iBAAiB,GAAG,8BAA8B,CAAC;IACzD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;AACjF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,iBAAqC;IAClE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,gBAAgB;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,cAAc,CAAC,KAAK,CAAC,EAArB,CAAqB;YACzD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,+EAA+E,EAA5F,CAA4F,EAC1G,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport matchesValidator from 'validator/lib/matches';\n\nexport const IS_MILITARY_TIME = 'isMilitaryTime';\n\n/**\n * Checks if the string represents a time without a given timezone in the format HH:MM (military)\n * If the given value does not match the pattern HH:MM, then it returns false.\n */\nexport function isMilitaryTime(value: unknown): boolean {\n  const militaryTimeRegex = /^([01]\\d|2[0-3]):?([0-5]\\d)$/;\n  return typeof value === 'string' && matchesValidator(value, militaryTimeRegex);\n}\n\n/**\n * Checks if the string represents a time without a given timezone in the format HH:MM (military)\n * If the given value does not match the pattern HH:MM, then it returns false.\n */\nexport function IsMilitaryTime(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MILITARY_TIME,\n      validator: {\n        validate: (value, args): boolean => isMilitaryTime(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid representation of military time in the format HH:MM',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}