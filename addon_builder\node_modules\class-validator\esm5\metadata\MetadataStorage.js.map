{"version": 3, "file": "MetadataStorage.js", "sourceRoot": "", "sources": ["../../../src/metadata/MetadataStorage.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,OAAO,EAAE,qCAAqC,EAAE,MAAM,4DAA4D,CAAC;AACnH,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAErC;;GAEG;AACH;IAAA;QACE,4EAA4E;QAC5E,qBAAqB;QACrB,4EAA4E;QAEpE,wBAAmB,GAAmC,IAAI,GAAG,EAAE,CAAC;QAChE,wBAAmB,GAAmC,IAAI,GAAG,EAAE,CAAC;IA6I1E,CAAC;IA3IC,sBAAI,kDAAqB;aAAzB;YACE,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;QACzC,CAAC;;;OAAA;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,6CAAmB,GAAnB,UAAoB,MAAwB;QAA5C,iBAGC;QAFC,IAAM,mBAAmB,GAAG,IAAI,qCAAqC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1F,mBAAmB,CAAC,OAAO,CAAC,UAAA,kBAAkB,IAAI,OAAA,KAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,EAA9C,CAA8C,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACH,+CAAqB,GAArB,UAAsB,QAA4B;QAChD,IAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEvE,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,+CAAqB,GAArB,UAAsB,QAA4B;QAChD,IAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEvE,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,6CAAmB,GAAnB,UAAoB,QAA8B;QAChD,IAAM,OAAO,GAAqD,EAAE,CAAC;QACrE,QAAQ,CAAC,OAAO,CAAC,UAAA,QAAQ;YACvB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YACzE,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,sDAA4B,GAA5B,UACE,iBAA2B,EAC3B,YAAoB,EACpB,MAAe,EACf,YAAqB,EACrB,MAAiB;;QAEjB,IAAM,oCAAoC,GAAG,UAAC,QAA4B;YACxE,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,WAAW;gBAAE,OAAO,QAAQ,CAAC,MAAM,CAAC;YAEnE,8CAA8C;YAC9C,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM;gBAAE,OAAO,KAAK,CAAC;YAE5D,sBAAsB;YACtB,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QAEF,IAAM,0CAA0C,GAAG,UAAC,QAA4B;YAC9E,IAAI,YAAY,EAAE,CAAC;gBACjB,kCAAkC;gBAClC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC9B,4CAA4C;oBAC5C,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM;wBAAE,OAAO,IAAI,CAAC;gBAC7D,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;QAEF,6CAA6C;QAC7C,IAAM,kCAAkC,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACjG,IAAM,iBAAiB,GAAG,kCAAkC,CAAC,MAAM,CAAC,UAAA,QAAQ;YAC1E,IAAI,QAAQ,CAAC,MAAM,KAAK,iBAAiB,IAAI,QAAQ,CAAC,MAAM,KAAK,YAAY;gBAAE,OAAO,KAAK,CAAC;YAC5F,IAAI,oCAAoC,CAAC,QAAQ,CAAC;gBAAE,OAAO,IAAI,CAAC;YAChE,IAAI,0CAA0C,CAAC,QAAQ,CAAC;gBAAE,OAAO,KAAK,CAAC;YACvE,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;gBAC7B,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAC;YAE1F,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAM,mCAAmC,GAAG,EAAE,CAAC;;YAC/C,KAA2B,IAAA,KAAA,SAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAA,gBAAA,4BAAE,CAAC;gBAArD,IAAA,KAAA,mBAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;gBACpB,IAAI,iBAAiB,CAAC,SAAS,YAAY,GAAG,EAAE,CAAC;oBAC/C,mCAAmC,CAAC,IAAI,OAAxC,mCAAmC,2BAAS,KAAK,WAAE;gBACrD,CAAC;YACH,CAAC;;;;;;;;;QACD,IAAM,kBAAkB,GAAG,mCAAmC,CAAC,MAAM,CAAC,UAAA,QAAQ;YAC5E,iHAAiH;YACjH,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,QAAQ;gBAAE,OAAO,KAAK,CAAC;YACtD,IAAI,QAAQ,CAAC,MAAM,KAAK,iBAAiB;gBAAE,OAAO,KAAK,CAAC;YACxD,IAAI,QAAQ,CAAC,MAAM,YAAY,QAAQ,IAAI,CAAC,CAAC,iBAAiB,CAAC,SAAS,YAAY,QAAQ,CAAC,MAAM,CAAC;gBAClG,OAAO,KAAK,CAAC;YACf,IAAI,oCAAoC,CAAC,QAAQ,CAAC;gBAAE,OAAO,IAAI,CAAC;YAChE,IAAI,0CAA0C,CAAC,QAAQ,CAAC;gBAAE,OAAO,KAAK,CAAC;YACvE,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;gBAC7B,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAC;YAE1F,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,2FAA2F;QAC3F,IAAM,wBAAwB,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAA,iBAAiB;YAC1E,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAA,gBAAgB;gBAC7C,OAAO,CACL,gBAAgB,CAAC,YAAY,KAAK,iBAAiB,CAAC,YAAY;oBAChE,gBAAgB,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI,CACjD,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,iBAAiB,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,uDAA6B,GAA7B,UAA8B,MAAgB;QAC5C,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IACH,sBAAC;AAAD,CAAC,AAnJD,IAmJC;;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB;IAChC,IAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAE3B,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC;QAC1C,MAAM,CAAC,6BAA6B,GAAG,IAAI,eAAe,EAAE,CAAC;IAC/D,CAAC;IAED,OAAO,MAAM,CAAC,6BAA6B,CAAC;AAC9C,CAAC", "sourcesContent": ["import { ValidationMetadata } from './ValidationMetadata';\nimport { ConstraintMetadata } from './ConstraintMetadata';\nimport { ValidationSchema } from '../validation-schema/ValidationSchema';\nimport { ValidationSchemaToMetadataTransformer } from '../validation-schema/ValidationSchemaToMetadataTransformer';\nimport { getGlobal } from '../utils';\n\n/**\n * Storage all metadatas.\n */\nexport class MetadataStorage {\n  // -------------------------------------------------------------------------\n  // Private properties\n  // -------------------------------------------------------------------------\n\n  private validationMetadatas: Map<any, ValidationMetadata[]> = new Map();\n  private constraintMetadatas: Map<any, ConstraintMetadata[]> = new Map();\n\n  get hasValidationMetaData(): boolean {\n    return !!this.validationMetadatas.size;\n  }\n\n  // -------------------------------------------------------------------------\n  // Public Methods\n  // -------------------------------------------------------------------------\n\n  /**\n   * Adds a new validation metadata.\n   */\n  addValidationSchema(schema: ValidationSchema): void {\n    const validationMetadatas = new ValidationSchemaToMetadataTransformer().transform(schema);\n    validationMetadatas.forEach(validationMetadata => this.addValidationMetadata(validationMetadata));\n  }\n\n  /**\n   * Adds a new validation metadata.\n   */\n  addValidationMetadata(metadata: ValidationMetadata): void {\n    const existingMetadata = this.validationMetadatas.get(metadata.target);\n\n    if (existingMetadata) {\n      existingMetadata.push(metadata);\n    } else {\n      this.validationMetadatas.set(metadata.target, [metadata]);\n    }\n  }\n\n  /**\n   * Adds a new constraint metadata.\n   */\n  addConstraintMetadata(metadata: ConstraintMetadata): void {\n    const existingMetadata = this.constraintMetadatas.get(metadata.target);\n\n    if (existingMetadata) {\n      existingMetadata.push(metadata);\n    } else {\n      this.constraintMetadatas.set(metadata.target, [metadata]);\n    }\n  }\n\n  /**\n   * Groups metadata by their property names.\n   */\n  groupByPropertyName(metadata: ValidationMetadata[]): { [propertyName: string]: ValidationMetadata[] } {\n    const grouped: { [propertyName: string]: ValidationMetadata[] } = {};\n    metadata.forEach(metadata => {\n      if (!grouped[metadata.propertyName]) grouped[metadata.propertyName] = [];\n      grouped[metadata.propertyName].push(metadata);\n    });\n    return grouped;\n  }\n\n  /**\n   * Gets all validation metadatas for the given object with the given groups.\n   */\n  getTargetValidationMetadatas(\n    targetConstructor: Function,\n    targetSchema: string,\n    always: boolean,\n    strictGroups: boolean,\n    groups?: string[]\n  ): ValidationMetadata[] {\n    const includeMetadataBecauseOfAlwaysOption = (metadata: ValidationMetadata): boolean => {\n      // `metadata.always` overrides global default.\n      if (typeof metadata.always !== 'undefined') return metadata.always;\n\n      // `metadata.groups` overrides global default.\n      if (metadata.groups && metadata.groups.length) return false;\n\n      // Use global default.\n      return always;\n    };\n\n    const excludeMetadataBecauseOfStrictGroupsOption = (metadata: ValidationMetadata): boolean => {\n      if (strictGroups) {\n        // Validation is not using groups.\n        if (!groups || !groups.length) {\n          // `metadata.groups` has at least one group.\n          if (metadata.groups && metadata.groups.length) return true;\n        }\n      }\n\n      return false;\n    };\n\n    // get directly related to a target metadatas\n    const filteredForOriginalMetadatasSearch = this.validationMetadatas.get(targetConstructor) || [];\n    const originalMetadatas = filteredForOriginalMetadatasSearch.filter(metadata => {\n      if (metadata.target !== targetConstructor && metadata.target !== targetSchema) return false;\n      if (includeMetadataBecauseOfAlwaysOption(metadata)) return true;\n      if (excludeMetadataBecauseOfStrictGroupsOption(metadata)) return false;\n      if (groups && groups.length > 0)\n        return metadata.groups && !!metadata.groups.find(group => groups.indexOf(group) !== -1);\n\n      return true;\n    });\n\n    // get metadatas for inherited classes\n    const filteredForInheritedMetadatasSearch = [];\n    for (const [key, value] of this.validationMetadatas.entries()) {\n      if (targetConstructor.prototype instanceof key) {\n        filteredForInheritedMetadatasSearch.push(...value);\n      }\n    }\n    const inheritedMetadatas = filteredForInheritedMetadatasSearch.filter(metadata => {\n      // if target is a string it's means we validate against a schema, and there is no inheritance support for schemas\n      if (typeof metadata.target === 'string') return false;\n      if (metadata.target === targetConstructor) return false;\n      if (metadata.target instanceof Function && !(targetConstructor.prototype instanceof metadata.target))\n        return false;\n      if (includeMetadataBecauseOfAlwaysOption(metadata)) return true;\n      if (excludeMetadataBecauseOfStrictGroupsOption(metadata)) return false;\n      if (groups && groups.length > 0)\n        return metadata.groups && !!metadata.groups.find(group => groups.indexOf(group) !== -1);\n\n      return true;\n    });\n\n    // filter out duplicate metadatas, prefer original metadatas instead of inherited metadatas\n    const uniqueInheritedMetadatas = inheritedMetadatas.filter(inheritedMetadata => {\n      return !originalMetadatas.find(originalMetadata => {\n        return (\n          originalMetadata.propertyName === inheritedMetadata.propertyName &&\n          originalMetadata.type === inheritedMetadata.type\n        );\n      });\n    });\n\n    return originalMetadatas.concat(uniqueInheritedMetadatas);\n  }\n\n  /**\n   * Gets all validator constraints for the given object.\n   */\n  getTargetValidatorConstraints(target: Function): ConstraintMetadata[] {\n    return this.constraintMetadatas.get(target) || [];\n  }\n}\n\n/**\n * Gets metadata storage.\n * Metadata storage follows the best practices and stores metadata in a global variable.\n */\nexport function getMetadataStorage(): MetadataStorage {\n  const global = getGlobal();\n\n  if (!global.classValidatorMetadataStorage) {\n    global.classValidatorMetadataStorage = new MetadataStorage();\n  }\n\n  return global.classValidatorMetadataStorage;\n}\n"]}