{"version": 3, "file": "ValidationUtils.js", "sourceRoot": "", "sources": ["../../../src/validation/ValidationUtils.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,UAAmB;IACpD,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9B,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACnC,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC;IACtC,CAAC;IAED,OAAO,UAAG,UAAU,CAAE,CAAC;AACzB,CAAC;AAED;IAAA;IAiCA,CAAC;IAhCQ,2CAA2B,GAAlC,UACE,OAAyD,EACzD,mBAAwC;QAExC,IAAI,aAAqB,CAAC;QAC1B,IAAI,OAAO,YAAY,QAAQ,EAAE,CAAC;YAChC,aAAa,GAAI,OAAiD,CAAC,mBAAmB,CAAC,CAAC;QAC1F,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvC,aAAa,GAAG,OAAO,CAAC;QAC1B,CAAC;QAED,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;YACpE,mBAAmB,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU,EAAE,KAAK;gBACxD,aAAa,GAAG,aAAa,CAAC,OAAO,CACnC,IAAI,MAAM,CAAC,uBAAgB,KAAK,GAAG,CAAC,CAAE,EAAE,GAAG,CAAC,EAC5C,kBAAkB,CAAC,UAAU,CAAC,CAC/B,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IACE,aAAa;YACb,mBAAmB,CAAC,KAAK,KAAK,SAAS;YACvC,mBAAmB,CAAC,KAAK,KAAK,IAAI;YAClC,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,mBAAmB,CAAC,KAAK,CAAC;YAE1E,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,aAAa;YAAE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACtG,IAAI,aAAa;YAAE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAEtG,OAAO,aAAa,CAAC;IACvB,CAAC;IACH,sBAAC;AAAD,CAAC,AAjCD,IAiCC", "sourcesContent": ["import { ValidationArguments } from './ValidationArguments';\n\n/**\n * Convert the constraint to a string to be shown in an error\n */\nexport function constraintToString(constraint: unknown): string {\n  if (Array.isArray(constraint)) {\n    return constraint.join(', ');\n  }\n\n  if (typeof constraint === 'symbol') {\n    constraint = constraint.description;\n  }\n\n  return `${constraint}`;\n}\n\nexport class ValidationUtils {\n  static replaceMessageSpecialTokens(\n    message: string | ((args: ValidationArguments) => string),\n    validationArguments: ValidationArguments\n  ): string {\n    let messageString: string;\n    if (message instanceof Function) {\n      messageString = (message as (args: ValidationArguments) => string)(validationArguments);\n    } else if (typeof message === 'string') {\n      messageString = message;\n    }\n\n    if (messageString && Array.isArray(validationArguments.constraints)) {\n      validationArguments.constraints.forEach((constraint, index) => {\n        messageString = messageString.replace(\n          new RegExp(`\\\\$constraint${index + 1}`, 'g'),\n          constraintToString(constraint)\n        );\n      });\n    }\n\n    if (\n      messageString &&\n      validationArguments.value !== undefined &&\n      validationArguments.value !== null &&\n      ['string', 'boolean', 'number'].includes(typeof validationArguments.value)\n    )\n      messageString = messageString.replace(/\\$value/g, validationArguments.value);\n    if (messageString) messageString = messageString.replace(/\\$property/g, validationArguments.property);\n    if (messageString) messageString = messageString.replace(/\\$target/g, validationArguments.targetName);\n\n    return messageString;\n  }\n}\n"]}