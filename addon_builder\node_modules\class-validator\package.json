{"name": "class-validator", "version": "0.14.1", "description": "Decorator-based property validation for classes.", "author": "TypeStack contributors", "license": "MIT", "sideEffects": false, "main": "./cjs/index.js", "module": "./esm5/index.js", "es2015": "./esm2015/index.js", "typings": "./types/index.d.ts", "repository": {"type": "git", "url": "https://github.com/typestack/class-validator.git"}, "tags": ["validator", "validation", "decorators", "typescript"], "dependencies": {"@types/validator": "^13.11.8", "libphonenumber-js": "^1.10.53", "validator": "^13.9.0"}}