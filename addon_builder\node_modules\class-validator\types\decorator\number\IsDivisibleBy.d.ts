import { ValidationOptions } from '../ValidationOptions';
export declare const IS_DIVISIBLE_BY = "isDivisibleBy";
/**
 * Checks if value is a number that's divisible by another.
 */
export declare function isDivisibleBy(value: unknown, num: number): boolean;
/**
 * Checks if value is a number that's divisible by another.
 */
export declare function IsDivisibleBy(num: number, validationOptions?: ValidationOptions): PropertyDecorator;
