import { ValidationOptions } from '../ValidationOptions';
export declare const IS_DATA_URI = "isDataURI";
/**
 * Check if the string is a data uri format.
 * If given value is not a string, then it returns false.
 */
export declare function isDataURI(value: unknown): boolean;
/**
 * Check if the string is a data uri format.
 * If given value is not a string, then it returns false.
 */
export declare function IsDataURI(validationOptions?: ValidationOptions): PropertyDecorator;
