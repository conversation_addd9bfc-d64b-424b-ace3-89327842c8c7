import { ValidationOptions } from '../ValidationOptions';
export declare const IS_ETHEREUM_ADDRESS = "isEthereumAddress";
/**
 * Check if the string is an Ethereum address using basic regex. Does not validate address checksums.
 * If given value is not a string, then it returns false.
 */
export declare function isEthereumAddress(value: unknown): boolean;
/**
 * Check if the string is an Ethereum address using basic regex. Does not validate address checksums.
 * If given value is not a string, then it returns false.
 */
export declare function IsEthereumAddress(validationOptions?: ValidationOptions): PropertyDecorator;
