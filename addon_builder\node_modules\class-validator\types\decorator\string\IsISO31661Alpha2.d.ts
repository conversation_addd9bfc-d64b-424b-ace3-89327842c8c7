import { ValidationOptions } from '../ValidationOptions';
export declare const IS_ISO31661_ALPHA_2 = "isISO31661Alpha2";
/**
 * Check if the string is a valid [ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) officially assigned country code.
 */
export declare function isISO31661Alpha2(value: unknown): boolean;
/**
 * Check if the string is a valid [ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) officially assigned country code.
 */
export declare function IsISO31661Alpha2(validationOptions?: ValidationOptions): PropertyDecorator;
