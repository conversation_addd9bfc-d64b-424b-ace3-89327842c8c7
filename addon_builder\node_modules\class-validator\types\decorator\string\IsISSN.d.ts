import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_ISSN = "isISSN";
/**
 * Checks if the string is a ISSN.
 * If given value is not a string, then it returns false.
 */
export declare function isISSN(value: unknown, options?: ValidatorJS.IsISSNOptions): boolean;
/**
 * Checks if the string is a ISSN.
 * If given value is not a string, then it returns false.
 */
export declare function IsISSN(options?: ValidatorJS.IsISSNOptions, validationOptions?: ValidationOptions): PropertyDecorator;
