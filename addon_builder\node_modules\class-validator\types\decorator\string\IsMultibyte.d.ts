import { ValidationOptions } from '../ValidationOptions';
export declare const IS_MULTIBYTE = "isMultibyte";
/**
 * Checks if the string contains one or more multibyte chars.
 * If given value is not a string, then it returns false.
 */
export declare function isMultibyte(value: unknown): boolean;
/**
 * Checks if the string contains one or more multibyte chars.
 * If given value is not a string, then it returns false.
 */
export declare function IsMultibyte(validationOptions?: ValidationOptions): PropertyDecorator;
