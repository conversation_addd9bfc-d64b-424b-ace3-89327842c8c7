import { ValidationOptions } from '../ValidationOptions';
export declare const IS_VARIABLE_WIDTH = "isVariableWidth";
/**
 * Checks if the string contains variable-width chars.
 * If given value is not a string, then it returns false.
 */
export declare function isVariableWidth(value: unknown): boolean;
/**
 * Checks if the string contains variable-width chars.
 * If given value is not a string, then it returns false.
 */
export declare function IsVariableWidth(validationOptions?: ValidationOptions): PropertyDecorator;
